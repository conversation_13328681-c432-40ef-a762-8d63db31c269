import {
  validateAmount,
  delay,
  showLoading,
  updateBalanceColor,
  showAlert,
} from "./utils.js";

let currentUser;
let balance;
let historyTable;

export function setupDashboard(user) {
  currentUser = user;
  balance = user.balance;
  historyTable = document.getElementById("historyTable");

  document.getElementById("customerName").textContent = user.name;
  document.getElementById("accountType").textContent = user.accountType;
  updateBalanceDisplay();

  document
    .getElementById("depositBtn")
    .addEventListener("click", () => handleTransaction("deposit"));
  document
    .getElementById("withdrawBtn")
    .addEventListener("click", () => handleTransaction("withdraw"));
}

function updateBalanceDisplay() {
  const balanceElem = document.getElementById("balance");
  balanceElem.textContent = balance;
  updateBalanceColor(balance);
}

async function handleTransaction(type) {
  const amountInput = document.getElementById(
    type === "deposit" ? "depositAmount" : "withdrawAmount"
  );
  const amount = parseFloat(amountInput.value);

  if (!validateAmount(amount)) {
    showAlert("Please enter a valid amount", "error");
    return;
  }

  if (type === "withdraw" && amount > balance) {
    showAlert("Insufficient balance for withdrawal", "error");
    return;
  }

  showLoading(true);
  await delay(1000);
  showLoading(false);

  balance += type === "deposit" ? amount : -amount;
  updateBalanceDisplay();
  addToHistory(type, amount, balance);

  // Show success message
  const message =
    type === "deposit"
      ? `₹${amount} deposited successfully!`
      : `₹${amount} withdrawn successfully!`;
  showAlert(message, "success");

  amountInput.value = "";
}

function addToHistory(type, amount, currentBalance) {
  const row = historyTable.insertRow(-1);
  row.insertCell(0).textContent = type;
  row.insertCell(1).textContent = `₹${amount}`;
  row.insertCell(2).textContent = `₹${currentBalance}`;
}
