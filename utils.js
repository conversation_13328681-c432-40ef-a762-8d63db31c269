export function delay(ms) {
  return new Promise((res) => setTimeout(res, ms));
}

export function validateAmount(amount) {
  return amount > 0 && !isNaN(amount);
}

export function showLoading(show) {
  const loading = document.getElementById("loading");
  if (show) {
    loading.classList.remove("hidden");
    let dots = 1;
    loading.interval = setInterval(() => {
      document.getElementById("dots").textContent = ".".repeat(
        (dots++ % 4) + 1
      );
    }, 500);
  } else {
    loading.classList.add("hidden");
    clearInterval(loading.interval);
  }
}

export function updateBalanceColor(balance) {
  const elem = document.getElementById("balance-text");
  if (balance < 500) elem.classList.add("red");
  else elem.classList.remove("red");
}

export function showAlert(message, type = "success", duration = 3000) {
  // Remove any existing alerts
  const existingAlert = document.querySelector(".float-alert");
  if (existingAlert) {
    existingAlert.remove();
  }

  // Create alert element
  const alert = document.createElement("div");
  alert.className = `float-alert ${type}`;
  alert.textContent = message;

  // Add to body
  document.body.appendChild(alert);

  // Trigger animation
  setTimeout(() => {
    alert.classList.add("show");
  }, 10);

  // Auto remove after duration
  setTimeout(() => {
    alert.classList.remove("show");
    setTimeout(() => {
      if (alert.parentNode) {
        alert.parentNode.removeChild(alert);
      }
    }, 300);
  }, duration);
}
