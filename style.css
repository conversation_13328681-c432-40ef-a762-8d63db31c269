/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: #f1f4f9;
  color: #333;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header/Banner Styles */
.banner {
  background: linear-gradient(
      135deg,
      rgba(0, 78, 146, 0.8),
      rgba(0, 4, 40, 0.8)
    ),
    url("banner.jpg") no-repeat center center;
  background-size: cover;
  color: white;
  padding: 40px 0;
  text-align: left;
  width: 100%;
}

.banner h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 700;
}

.banner p {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 40px 0;
}

/* Login Form Styles */
.login-form {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.form-container {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.form-container h2 {
  margin-bottom: 30px;
  color: #004e92;
  font-size: 1.8rem;
}

/* Input and Button Styles */
input,
button {
  padding: 12px 16px;
  margin: 10px 0;
  width: 100%;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
}

input:focus {
  outline: none;
  border-color: #004e92;
  box-shadow: 0 0 0 3px rgba(0, 78, 146, 0.1);
}

button {
  background: linear-gradient(135deg, #004e92, #000428);
  color: white;
  border: none;
  cursor: pointer;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

button:hover {
  background: linear-gradient(135deg, #003d75, #000320);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 78, 146, 0.3);
}

button:active {
  transform: translateY(0);
}

/* Dashboard Styles */
.dashboard-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  text-align: center;
}

.dashboard-header h2 {
  color: #004e92;
  margin-bottom: 15px;
  font-size: 2rem;
}

.operations-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.operation {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.operation h3 {
  color: #004e92;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

/* Transaction History */
.transaction-history {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.transaction-history h3 {
  color: #004e92;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
}

th,
td {
  border: 1px solid #e1e5e9;
  padding: 12px;
  text-align: left;
}

th {
  background: #f8f9fa;
  color: #004e92;
  font-weight: 600;
}

tr:nth-child(even) {
  background: #f8f9fa;
}

/* Footer Styles */
.footer {
  background: linear-gradient(135deg, #004e92, #000428);
  color: white;
  padding: 20px 0;
  text-align: center;
  width: 100%;
  margin-top: auto;
}

.footer p {
  margin: 0;
  opacity: 0.9;
}

/* Utility Classes */
.hidden {
  display: none;
}

#balance-text.red {
  color: #dc3545;
  font-weight: 600;
}

#login-msg {
  color: #dc3545;
  margin-top: 15px;
  font-weight: 500;
}

#loading {
  text-align: center;
  color: #004e92;
  font-size: 1.2rem;
  margin: 20px 0;
}

/* Float Alert Styles */
.float-alert {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 25px;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  font-size: 16px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  transform: translateX(400px);
  opacity: 0;
  transition: all 0.3s ease;
  max-width: 350px;
  word-wrap: break-word;
}

.float-alert.show {
  transform: translateX(0);
  opacity: 1;
}

.float-alert.success {
  background: linear-gradient(135deg, #28a745, #20c997);
  border-left: 4px solid #1e7e34;
}

.float-alert.error {
  background: linear-gradient(135deg, #dc3545, #e74c3c);
  border-left: 4px solid #c82333;
}

.float-alert.warning {
  background: linear-gradient(135deg, #ffc107, #ffca2c);
  color: #212529;
  border-left: 4px solid #e0a800;
}

.float-alert.info {
  background: linear-gradient(135deg, #004e92, #17a2b8);
  border-left: 4px solid #003d75;
}

/* Responsive Design */
@media (max-width: 768px) {
  .banner h1 {
    font-size: 2rem;
  }

  .banner p {
    font-size: 1rem;
  }

  .form-container {
    margin: 0 20px;
    padding: 30px 20px;
  }

  .operations-container {
    grid-template-columns: 1fr;
  }

  .container {
    padding: 0 15px;
  }
}
