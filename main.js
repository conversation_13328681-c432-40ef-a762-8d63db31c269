import { login } from "./auth.js";
import { setupDashboard } from "./dashboard.js";

document.getElementById("loginBtn").addEventListener("click", async () => {
  const id = document.getElementById("customerId").value;
  const password = document.getElementById("password").value;

  const user = await login(id, password);
  if (user) {
    document.getElementById("login-section").classList.add("hidden");
    document.getElementById("dashboard-section").classList.remove("hidden");
    setupDashboard(user);
  } else {
    document.getElementById("login-msg").textContent = "Invalid credentials";
  }
});
