<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bank Portal</title>
    <link rel="stylesheet" href="style.css" />
</head>

<body>
    <div id="login-section">
        <h2>Login</h2>
        <input type="text" id="customerId" placeholder="Customer ID" />
        <input type="password" id="password" placeholder="Password" />
        <button id="loginBtn">Login</button>
        <div id="login-msg"></div>
    </div>

    <div id="dashboard-section" class="hidden">
        <h2>Welcome, <span id="customerName"></span></h2>
        <p>Account Type: <span id="accountType"></span></p>
        <p id="balance-text">Current Balance: ₹<span id="balance">0</span></p>

        <div class="operation">
            <h3>Deposit</h3>
            <input type="number" id="depositAmount" placeholder="Enter amount" />
            <button id="depositBtn">Deposit</button>
        </div>

        <div class="operation">
            <h3>Withdraw</h3>
            <input type="number" id="withdrawAmount" placeholder="Enter amount" />
            <button id="withdrawBtn">Withdraw</button>
        </div>

        <div id="loading" class="hidden">Processing<span id="dots">.</span></div>

        <h3>Transaction History</h3>
        <table id="historyTable">
            <tr>
                <th>Type</th>
                <th>Amount</th>
                <th>Balance</th>
            </tr>
        </table>
    </div>

    <script type="module" src="main.js"></script>
</body>

</html>